/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.basic.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * 零售许可证信息日切表 视图实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Schema(description = "零售许可证信息日切表VO")
public class YhytLicenseDailyCutVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 连锁企业品牌
	 */
	@Schema(description = "连锁企业品牌")
	private String lsqypp;

	/**
	 * 次级商圈
	 */
	@Schema(description = "次级商圈")
	private String cjsq;

	/**
	 * 连续未订货时间
	 */
	@Schema(description = "连续未订货时间")
	private String lxwdhsj;

	/**
	 * 供货单位名称
	 */
	@Schema(description = "供货单位名称")
	private String ghdwmc;

	/**
	 * 从事电子烟零售起始时间
	 */
	@Schema(description = "从事电子烟零售起始时间")
	private String csdzylsqssj;

	/**
	 * 纬度
	 */
	@Schema(description = "纬度")
	private String wd;

	/**
	 * 消费需求
	 */
	@Schema(description = "消费需求")
	private String xfxq;

	/**
	 * 经营场所邮编
	 */
	@Schema(description = "经营场所邮编")
	private String jycsyb;

	/**
	 * 市局名称
	 */
	@Schema(description = "市局名称")
	private String sjmc;

	/**
	 * 许可证经营范围多选
	 */
	@Schema(description = "许可证经营范围多选")
	private String xkzjyfwdx;

	/**
	 * 最后检查日期-市管维护字段
	 */
	@Schema(description = "最后检查日期-市管维护字段")
	private String zhjcrqsgwhzd;

	/**
	 * 地理环境
	 */
	@Schema(description = "地理环境")
	private String dlhj;

	/**
	 * 创建时间
	 */
	@Schema(description = "创建时间")
	private String cjsj;

	/**
	 * 联系人
	 */
	@Schema(description = "联系人")
	private String lxr;

	/**
	 * 是否经营电子烟
	 */
	@Schema(description = "是否经营电子烟")
	private String sfjydzy;

	/**
	 * 客户编码
	 */
	@Schema(description = "客户编码")
	private String khbm;

	/**
	 * 经度
	 */
	@Schema(description = "经度")
	private String jd;

	/**
	 * 领证（送达）日期
	 */
	@Schema(description = "领证（送达）日期")
	private String lzsdrq;

	/**
	 * 片区
	 */
	@Schema(description = "片区")
	private String pq;

	/**
	 * 信息完率
	 */
	@Schema(description = "信息完率")
	private String xxwl;

	/**
	 * 市场类型细分
	 */
	@Schema(description = "市场类型细分")
	private String sclxxf;

	/**
	 * 经济类型-子类
	 */
	@Schema(description = "经济类型-子类")
	private String jjlxzl;

	/**
	 * 其他特殊群体说明
	 */
	@Schema(description = "其他特殊群体说明")
	private String qttsqtsm;

	/**
	 * 经营地址
	 */
	@Schema(description = "经营地址")
	private String jydz;

	/**
	 * 负责人证件类型
	 */
	@Schema(description = "负责人证件类型")
	private String fzrzjlx;

	/**
	 * 发证机关名称
	 */
	@Schema(description = "发证机关名称")
	private String fzjgmc;

	/**
	 * gm编号
	 */
	@Schema(description = "gm编号")
	private String gmbh;

	/**
	 * 发证机关uuid(废弃)
	 */
	@Schema(description = "发证机关uuid(废弃)")
	private String fzjguuidfq;

	/**
	 * 场地权属有效期类型
	 */
	@Schema(description = "场地权属有效期类型")
	private String cdqsyxqlx;

	/**
	 * 发证机关编码
	 */
	@Schema(description = "发证机关编码")
	private String fzjgbm;

	/**
	 * 业态细分
	 */
	@Schema(description = "业态细分")
	private String ytxf;

	/**
	 * 行政区划代码
	 */
	@Schema(description = "行政区划代码")
	private String xzqhdm;

	/**
	 * 分区字段
	 */
	@Schema(description = "分区字段")
	private String fqzd;

	/**
	 * 仓储地址
	 */
	@Schema(description = "仓储地址")
	private String ccdz;

	/**
	 * 省重点流通企业
	 */
	@Schema(description = "省重点流通企业")
	private String szdltqy;

	/**
	 * 地缘情况
	 */
	@Schema(description = "地缘情况")
	private String dyqk;

	/**
	 * 行政区划（任意层级）
	 */
	@Schema(description = "行政区划（任意层级）")
	private String xzqhrycj;

	/**
	 * 工商营业执照编码（统一社会信用代码证）
	 */
	@Schema(description = "工商营业执照编码（统一社会信用代码证）")
	private String gsyyzzbmtyshxydmz;

	/**
	 * 群体类型
	 */
	@Schema(description = "群体类型")
	private String qtlx;

	/**
	 * 系统编码
	 */
	@Schema(description = "系统编码")
	private String xtbm;

	/**
	 * 是否有工商营业执照
	 */
	@Schema(description = "是否有工商营业执照")
	private String sfygsyyzz;

	/**
	 * 是否留存既往电子烟经营记录
	 */
	@Schema(description = "是否留存既往电子烟经营记录")
	private String sflcjwdzyjyjl;

	/**
	 * 电子证照标识
	 */
	@Schema(description = "电子证照标识")
	private String dzzzbs;

	/**
	 * 当事人名称
	 */
	@Schema(description = "当事人名称")
	private String dsrmc;

	/**
	 * 企业经济类型
	 */
	@Schema(description = "企业经济类型")
	private String qyjjlx;

	/**
	 * 工商营业执照注册资本
	 */
	@Schema(description = "工商营业执照注册资本")
	private String gsyyzzzczb;

	/**
	 * 许可证有效期限止
	 */
	@Schema(description = "许可证有效期限止")
	private String xkzyxqxz;

	/**
	 * 制证日期
	 */
	@Schema(description = "制证日期")
	private String zzrq;

	/**
	 * 老许可证号
	 */
	@Schema(description = "老许可证号")
	private String lxkzh;

	/**
	 * 许可证是否有效
	 */
	@Schema(description = "许可证是否有效")
	private String xkzsfyx;

	/**
	 * 最后检查日期-最后双随机检查时间
	 */
	@Schema(description = "最后检查日期-最后双随机检查时间")
	private String zhjcrqzhssjjcsj;

	/**
	 * 是否连锁
	 */
	@Schema(description = "是否连锁")
	private String sfls;

	/**
	 * 社区编码
	 */
	@Schema(description = "社区编码")
	private String sqbm;

	/**
	 * 失效时间（许可证注销时间）
	 */
	@Schema(description = "失效时间（许可证注销时间）")
	private String sxsjxkzzxsj;

	/**
	 * 营业执照有效期起
	 */
	@Schema(description = "营业执照有效期起")
	private String yyzzyxqq;

	/**
	 * 地区类别
	 */
	@Schema(description = "地区类别")
	private String dqlb;

	/**
	 * 商圈
	 */
	@Schema(description = "商圈")
	private String sq;

	/**
	 * 供货单位编号
	 */
	@Schema(description = "供货单位编号")
	private String ghdwbh;

	/**
	 * 备用电话
	 */
	@Schema(description = "备用电话")
	private String bydh;

	/**
	 * 发证日期
	 */
	@Schema(description = "发证日期")
	private String fzrq;

	/**
	 * 负责人证件号码
	 */
	@Schema(description = "负责人证件号码")
	private String fzrzjhm;

	/**
	 * 经济类型其它情况
	 */
	@Schema(description = "经济类型其它情况")
	private String jjlxqtqk;

	/**
	 * 是否独立住所
	 */
	@Schema(description = "是否独立住所")
	private String sfdlzs;

	/**
	 * 是否已生成电子证照
	 */
	@Schema(description = "是否已生成电子证照")
	private String sfyscdzzz;

	/**
	 * 三统一中原许可证uuid
	 */
	@Schema(description = "三统一中原许可证uuid")
	private String styzyxkzuuid;

	/**
	 * 企业名称
	 */
	@Schema(description = "企业名称")
	private String qymc;

	/**
	 * 片区名称
	 */
	@Schema(description = "片区名称")
	private String pqmc;

	/**
	 * 工商营业执照状态
	 */
	@Schema(description = "工商营业执照状态")
	private String gsyyzzzt;

	/**
	 * 联系电话
	 */
	@Schema(description = "联系电话")
	private String lxdh;

	/**
	 * 经营场地权属
	 */
	@Schema(description = "经营场地权属")
	private String jycdqs;

	/**
	 * 租赁期限起
	 */
	@Schema(description = "租赁期限起")
	private String zlqxq;

	/**
	 * 许可证有效期限起
	 */
	@Schema(description = "许可证有效期限起")
	private String xkzyxqxq;

	/**
	 * 是否曾申请办证
	 */
	@Schema(description = "是否曾申请办证")
	private String sfcsqbz;

	/**
	 * 许可证编号
	 */
	@Schema(description = "许可证编号")
	private String xkzbh;

	/**
	 * 申请停业期限止
	 */
	@Schema(description = "申请停业期限止")
	private String sqtyqxz;

	/**
	 * 创建人
	 */
	@Schema(description = "创建人")
	private String cjr;

	/**
	 * 修改时间
	 */
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String xgsj;

	/**
	 * 零售业态
	 */
	@Schema(description = "零售业态")
	private String lsyt;

	/**
	 * 供货单位编码
	 */
	@Schema(description = "供货单位编码")
	private String ghdwbm;

	/**
	 * 租金
	 */
	@Schema(description = "租金")
	private String zj;

	/**
	 * 是否二维码制卡
	 */
	@Schema(description = "是否二维码制卡")
	private String sfewmzk;

	/**
	 * 检查次数
	 */
	@Schema(description = "检查次数")
	private String jccs;

	/**
	 * 行业唯一标识
	 */
	@Schema(description = "行业唯一标识")
	private String xywybs;

	/**
	 * 营业执照有效期类型
	 */
	@Schema(description = "营业执照有效期类型")
	private String yyzzyxqlx;

	/**
	 * nfc卡号
	 */
	@Schema(description = "nfc卡号")
	private String nfckh;

	/**
	 * 零售户uuid
	 */
	@Schema(description = "零售户uuid")
	private String lshuuid;

	/**
	 * 是否新开户
	 */
	@Schema(description = "是否新开户")
	private String sfxkh;

	/**
	 * 申请停业期限起
	 */
	@Schema(description = "申请停业期限起")
	private String sqtyqxq;

	/**
	 * 修改人
	 */
	@Schema(description = "修改人")
	private String xgr;

	/**
	 * 连锁经营形式
	 */
	@Schema(description = "连锁经营形式")
	private String lsjyxs;

	/**
	 * 营业执照有效期止
	 */
	@Schema(description = "营业执照有效期止")
	private String yyzzyxqz;

	/**
	 * 申请人头像
	 */
	@Schema(description = "申请人头像")
	private String sqrtx;

	/**
	 * 最后检查年份-市管维护字段
	 */
	@Schema(description = "最后检查年份-市管维护字段")
	private String zhjcnfsgwhzd;

	/**
	 * 租赁期限止
	 */
	@Schema(description = "租赁期限止")
	private String zlqxz;

	/**
	 * 供货状态
	 */
	@Schema(description = "供货状态")
	private String ghzt;

	/**
	 * 市局编码
	 */
	@Schema(description = "市局编码")
	private String sjbm;

	/**
	 * 仓储面积
	 */
	@Schema(description = "仓储面积")
	private String ccmj;

	/**
	 * 信息完整度
	 */
	@Schema(description = "信息完整度")
	private String xxwzd;

	/**
	 * 首次制证日期
	 */
	@Schema(description = "首次制证日期")
	private String sczzrq;

	/**
	 * 经营面积
	 */
	@Schema(description = "经营面积")
	private String jymj;

	/**
	 * 店铺招牌
	 */
	@Schema(description = "店铺招牌")
	private String dpzp;

	/**
	 * 业务日期
	 */
	@Schema(description = "业务日期")
	private String ywrq;

	/**
	 * 上一级电子烟供应商名称
	 */
	@Schema(description = "上一级电子烟供应商名称")
	private String syjdzygysmc;

	/**
	 * 街道编码
	 */
	@Schema(description = "街道编码")
	private String jdbm;

	/**
	 * 许可证状态
	 */
	@Schema(description = "许可证状态")
	private String xkzzt;

	/**
	 * 照顾政策
	 */
	@Schema(description = "照顾政策")
	private String zgzc;

}
