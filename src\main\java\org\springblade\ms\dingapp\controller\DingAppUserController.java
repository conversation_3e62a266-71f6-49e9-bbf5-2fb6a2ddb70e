package org.springblade.ms.dingapp.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.modules.auth.utils.TokenUtil;
import org.springblade.ms.dingapp.service.IDingAppUserService;
import org.springblade.ms.dingapp.util.DdConfigSign;
import org.springblade.ms.dingapp.vo.DingUserAccessTokenVO;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-22 10:58
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/dingapp/user")
public class DingAppUserController {

    private IDingAppUserService dingAppUserService;

    @PostMapping("/login")
    public Kv login(@Valid @RequestBody Map<String, Object> dataMap) throws Exception {
        String authCode = (String) dataMap.get("authCode");
        Kv login = dingAppUserService.login(authCode);
        return login;
    }


    @GetMapping("getDingTalkApiParam")
    public Kv getDingTalkApiParam(@RequestParam(name = "url")String url,
                                      @RequestParam(name = "nonceStr")String nonceStr,
                                      @RequestParam(name = "timeStamp")Long timeStamp){
        Kv kv=Kv.create();
        try{
            String ticket=dingAppUserService.getJsapiTicket();
            String signature= DdConfigSign.sign(ticket,nonceStr,timeStamp,url);
            kv.put("code",200);
            kv.put("signature",signature);
        }catch (Exception e){
            kv.put("code",500);
            kv.put("signature",e.getMessage());
            log.error(e.getMessage());
        }
        return kv;
    }


}
