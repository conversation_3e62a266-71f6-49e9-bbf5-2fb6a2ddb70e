package org.springblade.ms.dingapp.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.service.IDingIllegalRecordsService;
import org.springblade.ms.illegalrecords.pojo.entity.IllegalRecordsEntity;
import org.springblade.ms.itemidentify.service.IItemIdentifyService;
import org.springblade.ms.reportcomplaint.mapper.ReportIllegalLabelMapper;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-16 18:36
 * 违法记录
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/illegalrecords")
public class DingIllegalRecordsController extends BladeController {

    private final ReportIllegalLabelMapper reportIllegalLabelMapper;
    private final IReportComplaintService reportComplaintService;
    private final IItemIdentifyService itemIdentifyService;
    private final IReportIllegalLabelService iReportIllegalLabelService;
    private final IDingIllegalRecordsService dingIllegalRecordsService;
    @GetMapping("/getListByCustCode")
    @ApiOperationSupport(order = 2)
    @Operation( description  = "传入yhytId")
    public R<List<IllegalRecordsEntity>> getListByCustCode(String yhytId) {
        List<IllegalRecordsEntity> illegalRecordsVOList = dingIllegalRecordsService.getListByCustCode(yhytId);
        return R.data(illegalRecordsVOList);
    }

    @GetMapping("/getTotalCount")
    @ApiOperationSupport(order = 2)
    @Operation( description  = "传入custCode,licNo")
    public R<JSONObject> getTotalCount(String yhytId, String licNo) {
        JSONObject jsonObject = new JSONObject();
        //案件（违法记录）数量
        QueryWrapper<ReportIllegalLabelEntity> qw = new QueryWrapper<>();
        qw.eq("label_id",Long.parseLong(yhytId))
                .eq("is_deleted",0)
                .eq("obj_type","案件");
        Long l = reportIllegalLabelMapper.selectCount(qw);
        jsonObject.putOnce("illegalRecordCount",l);

        //工单（举报投诉）数量
        QueryWrapper<ReportIllegalLabelEntity> qw1 = new QueryWrapper<>();
        qw1.eq("label_id",Long.parseLong(yhytId))
                .eq("is_deleted",0)
                .eq("obj_type","工单").groupBy("obj_id");
        Long l1 = reportIllegalLabelMapper.selectCount(qw1);
        jsonObject.putOnce("reportComplaintCount", l1);

        //品规识别数量
        Integer dateCountByLicenseId = itemIdentifyService.getDateCountByLicenseId(licNo);
        jsonObject.putOnce("identCount",dateCountByLicenseId);
        return R.data(jsonObject);
    }

    /**
     * 获取最近一年的违法记录、举报投诉数量
     */
    @GetMapping("/getLastYearTotalCount")
    @ApiOperationSupport(order = 2)
    @Operation( description  = "传入custCode,licNo")
    public R<JSONObject> getLastOneYearCountByCustCode(Long yhytId) {

        JSONObject jsonObject = new JSONObject();

        Long l = iReportIllegalLabelService.getIllegalRecordsLastYearCount(yhytId);
        jsonObject.putOnce("illegalRecordCount",l);



        QueryWrapper<ReportComplaintEntity> qw1 = new QueryWrapper<>();
        qw1.eq("user_id",yhytId)
            .apply("roll_date::date >= NOW() - INTERVAL '1 year'");
        List<ReportComplaintEntity> list1 = reportComplaintService.list(qw1);
        jsonObject.putOnce("reportComplaintCount", list1.size());

        return R.data(jsonObject);
    }
}
