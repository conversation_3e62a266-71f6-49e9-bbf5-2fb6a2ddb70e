package org.springblade.ms.dingapp.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.ms.dingapp.dto.ReportIllegalLabelSubmitDTO;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/dingapp/reportIllegalLabel")
public class DingReportIllegalLabelController {

    private final IReportIllegalLabelService reportIllegalLabelService;
    private final IReportComplaintService reportComplaintService;



    /**
     * 工单信息 新增或修改
     */
    @PostMapping("/submit")
    public R submit(@Valid @RequestBody ReportIllegalLabelSubmitDTO submitDTO) {
        // 先保存或更新标签信息
        boolean result = reportIllegalLabelService.saveOrUpdate(submitDTO.getObjId(), submitDTO.getReportIllegalLabelList());

        // 更新工单处理结果和状态
        if (result) {
            ReportComplaintEntity reportComplaint = reportComplaintService.getById(submitDTO.getObjId());
            if (reportComplaint != null) {
                // 设置处理结果字段
                reportComplaint.setHandleResult(submitDTO.getHandleResult());
                // 更新工单状态
                reportComplaint.setComplaintStatus("已处理");
                // 更新工单信息
                reportComplaintService.updateById(reportComplaint);
            }
        }

        return R.status(result);
    }
}
