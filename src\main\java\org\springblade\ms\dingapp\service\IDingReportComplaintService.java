package org.springblade.ms.dingapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.illegalrecords.pojo.vo.IllegalRecordsVO;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-16 18:39
 */
public interface IDingReportComplaintService {

    IPage<ReportComplaintVO> selectPage(IPage<ReportComplaintEntity> page, Long userId);

    IPage<ReportComplaintVO> selectPageByUserId(IPage<ReportComplaintEntity> page, Long userId);

}
