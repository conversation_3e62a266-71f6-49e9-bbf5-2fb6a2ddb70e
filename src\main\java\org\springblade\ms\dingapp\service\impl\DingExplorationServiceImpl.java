package org.springblade.ms.dingapp.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseUnlicensedEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.basic.service.YhytLicenseUnlicensedService;
import org.springblade.ms.basic.wrapper.YhytLicenseWrapper;
import org.springblade.ms.dingapp.service.IDingExplorationService;
import org.springblade.ms.dingapp.service.IDingLicenseService;
import org.springblade.ms.dingapp.vo.DingExplorationVO;
import org.springblade.ms.dingapp.vo.DingMapLicenseVO;
import org.springblade.ms.exploration.pojo.entity.ExplorationEntity;
import org.springblade.ms.exploration.pojo.vo.ExplorationVO;
import org.springblade.ms.exploration.service.IExplorationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 钉钉勘查记录服务类
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-12 09:43
 */
@Service
@AllArgsConstructor
public class DingExplorationServiceImpl implements IDingExplorationService {

    private final IDingLicenseService dingLicenseService;

    private final IExplorationService explorationService;

    private final IUploadFileService uploadFileService;

    private final YhytLicenseUnlicensedService yhytLicenseUnlicensedService;
    @Override
    public DingExplorationVO getTodayExploration(String lincNo) {
        DingExplorationVO dingExplorationVO = new DingExplorationVO();
        // 获取当天的yyyy-MM-dd
        DingMapLicenseVO dingMapLicense = null;
        if (StrUtil.isNumeric(lincNo)) {
            dingMapLicense = dingLicenseService.getDingMapLicense(null, lincNo);
        } else { //无证户查询
            QueryWrapper<YhytLicenseUnlicensedEntity> qw = new QueryWrapper<>();
            qw.like("company_name", lincNo);
            List<YhytLicenseUnlicensedEntity> list = yhytLicenseUnlicensedService.list(qw);
            if(list.isEmpty()){
                return null;
            }
            YhytLicenseUnlicensedEntity one = list.get(0);
            // 4. 将无证零售户转换为有证零售户格式
            YhytLicenseEntity entity = new YhytLicenseEntity();
            BeanUtil.copyProperties(one, entity);

            //转换为VO对象
            YhytLicenseVO yhytLicenseVO = YhytLicenseWrapper.build().entityVO(entity);
            dingMapLicense = new DingMapLicenseVO(yhytLicenseVO);
        }
        if (dingMapLicense == null) {
            return null;
        }

        List<UploadFileEntity> fileList1 = uploadFileService.selectCenterPhotoFileList(dingMapLicense.getId());
        dingMapLicense.setLastCenterPoho(fileList1);
        dingExplorationVO.setDingMapLicenseVO(dingMapLicense);


        ExplorationVO explorationVO = new ExplorationVO();
        explorationVO.setLicenseId(dingMapLicense.getId());
        explorationVO.setExplorationDate(DateUtil.date());
        ExplorationVO exploration = explorationService.getExplorationByParam(explorationVO);

        if (exploration != null) {
            dingExplorationVO.setExplorationVO(exploration);
        } else {
            ExplorationEntity entity = new ExplorationEntity();
            entity.setLicenseId(dingMapLicense.getId());
            entity.setExplorationDate(DateUtil.date());
            entity.setCreateType("搜索");
            explorationService.save(entity);
            dingExplorationVO.setExplorationVO(BeanUtil.copyProperties(entity, ExplorationVO.class));
        }

        //额外获取图片信息
        List<UploadFileEntity> fileList = uploadFileService.selectSurveyFileList(dingExplorationVO.getExplorationVO().getId());
        dingExplorationVO.getDingMapLicenseVO().setPhotoPathList(fileList);


        return dingExplorationVO;
    }

}
