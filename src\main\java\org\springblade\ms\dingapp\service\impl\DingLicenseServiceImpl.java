package org.springblade.ms.dingapp.service.impl;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ms.basic.pojo.entity.UploadFileEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseEntity;
import org.springblade.ms.basic.pojo.entity.YhytLicenseUnlicensedEntity;
import org.springblade.ms.basic.pojo.vo.YhytLicenseVO;
import org.springblade.ms.basic.service.IUploadFileService;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.basic.service.YhytLicenseUnlicensedService;
import org.springblade.ms.basic.wrapper.YhytLicenseWrapper;
import org.springblade.ms.dingapp.service.IDingLicenseService;
import org.springblade.ms.dingapp.vo.DingMapLicenseVO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-24 00:08
 */
@Service
@AllArgsConstructor
@Slf4j
public class DingLicenseServiceImpl implements IDingLicenseService {

    private final IYhytLicenseService yhytLicenseService;

    private final IUploadFileService uploadFileService;

    private final YhytLicenseUnlicensedService yhytLicenseUnlicensedService;

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public List<DingMapLicenseVO> getDingMapLicenseList(String searchParam, String formatParam, BigDecimal longitude, BigDecimal latitude) {

        YhytLicenseEntity param = new YhytLicenseEntity();
        if (StrUtil.isNotBlank(searchParam)) {
            // 判断searchParam内容是否为纯数字
            if (searchParam.matches("^[0-9]+$")) {
                param.setLicNo(searchParam);
            } else {
                param.setCompanyName(searchParam);
            }
        }

        if (StrUtil.isNotBlank(formatParam)) {
            if(!(formatParam.equals("全部"))){
                param.setBizFormat(formatParam);
            }
        }

        if (longitude != null && latitude != null) {
            param.setLongitude(longitude);
            param.setLatitude(latitude);
        }

        List<YhytLicenseEntity> list = yhytLicenseService.getDingMapList(param);

        if (IterUtil.isEmpty(list)) {
            return null;
        }

        // 转换对象
        List<DingMapLicenseVO> dingMapLicenseVOList = new ArrayList<>();
        for (YhytLicenseEntity entity : list) {
            DingMapLicenseVO dingMapLicenseVO = new DingMapLicenseVO(entity);
//            dingMapLicenseVO.setYhytLicenseVO(BeanUtil.copyProperties(entity, YhytLicenseVO.class));
            dingMapLicenseVOList.add(dingMapLicenseVO);
        }

//        if (longitude != null && latitude != null) {
//            for (DingMapLicenseVO dingMapLicenseVO : dingMapLicenseVOList) {
//                if (dingMapLicenseVO.getLatitude() == null || dingMapLicenseVO.getLongitude() == null)
//                    continue;
//                dingMapLicenseVO.setDistance(DistanceUtil.calculateDistance(latitude.doubleValue(), longitude.doubleValue(), dingMapLicenseVO.getLatitude().doubleValue(), dingMapLicenseVO.getLongitude().doubleValue()));
//            }
//        }

        return dingMapLicenseVOList;
    }

    private static final String REDIS_KEY_PREFIX = "ms:dingapp:license:";
    private static final int REDIS_EXPIRE_DAYS = 1; // 1天过期

    @Override
    public DingMapLicenseVO getDingMapLicense(String yhytId, String licNo) {
        log.debug("开始获取零售户许可证信息: yhytId={}, licNo={}", yhytId, licNo);

        // 参数校验
        if (StrUtil.isBlank(yhytId) && StrUtil.isBlank(licNo)) {
            log.warn("获取零售户许可证信息失败: 参数为空");
            return null;
        }

        // 构建缓存键
        String cacheKey = buildCacheKey(yhytId, licNo);

        // 1. 尝试从Redis缓存获取
        DingMapLicenseVO cachedResult = getCachedLicense(cacheKey);
        if (cachedResult != null) {
            log.debug("从缓存获取零售户许可证信息成功");
            // 1.1获取门面照片
            List<UploadFileEntity> fileList = uploadFileService.selectCenterPhotoFileList(cachedResult.getId());
            cachedResult.setPhotoPathList(fileList);
            return cachedResult;
        }

        // 2. 缓存未命中，从数据库获取
        DingMapLicenseVO result = fetchLicenseFromDatabase(yhytId, licNo);

        // 3. 如果获取到结果，更新缓存
        if (result != null) {
            cacheLicense(cacheKey, result);
        }else{
            return null;
        }

        // 4.获取门面照片
        List<UploadFileEntity> fileList = uploadFileService.selectCenterPhotoFileList(result.getId());
        result.setPhotoPathList(fileList);

        return result;
    }

    /**
     * 从Redis缓存获取许可证信息
     */
    private DingMapLicenseVO getCachedLicense(String cacheKey) {
        try {
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                log.debug("缓存命中: {}", cacheKey);
                DingMapLicenseVO result = (DingMapLicenseVO) cachedValue;
                return result;
            }
        } catch (Exception e) {
            log.error("从缓存获取许可证信息失败: {}", cacheKey, e);
        }
        return null;
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String yhytId, String licNo) {
        if (StrUtil.isNotBlank(yhytId)) {
            return REDIS_KEY_PREFIX + "id:" + yhytId;
        } else {
            return REDIS_KEY_PREFIX + "licNo:" + licNo;
        }
    }

    /**
     * 将许可证信息存入Redis缓存
     */
    private void cacheLicense(String cacheKey, DingMapLicenseVO licenseVO) {
        try {
            // 存入缓存并设置过期时间
            redisTemplate.opsForValue().set(cacheKey, licenseVO, REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            log.debug("更新缓存: {}", cacheKey);
        } catch (Exception e) {
            log.error("缓存许可证信息失败: {}", cacheKey, e);
        }
    }

    /**
     * 从数据库获取许可证信息
     */
    private DingMapLicenseVO fetchLicenseFromDatabase(String yhytId, String licNo) {
        // 1. 查询有证零售户
        YhytLicenseEntity entity = fetchLicensedEntity(yhytId, licNo);

        // 2. 如果没有找到有证零售户，查询无证零售户
        YhytLicenseUnlicensedEntity unlicensedEntity = null;
        if (entity == null) {
            unlicensedEntity = fetchUnlicensedEntity(yhytId, licNo);

            // 3. 如果两者都没找到，返回null
            if (unlicensedEntity == null) {
                log.debug("未找到零售户信息: yhytId={}, licNo={}", yhytId, licNo);
                return null;
            }

            // 4. 将无证零售户转换为有证零售户格式
            entity = new YhytLicenseEntity();
            BeanUtil.copyProperties(unlicensedEntity, entity);
        }

        // 5. 转换为VO对象
        YhytLicenseVO yhytLicenseVO = YhytLicenseWrapper.build().entityVO(entity);
        DingMapLicenseVO dingMapLicenseVO = new DingMapLicenseVO(yhytLicenseVO);

        return dingMapLicenseVO;
    }

    /**
     * 获取有证零售户信息
     */
    private YhytLicenseEntity fetchLicensedEntity(String yhytId, String licNo) {
        if (StrUtil.isBlank(yhytId) && StrUtil.isBlank(licNo)) {
            return null;
        }

        YhytLicenseEntity param = new YhytLicenseEntity();
        if(StrUtil.isNotBlank(yhytId)) {
            param.setId(Long.parseLong(yhytId));
        }
        if(StrUtil.isNotBlank(licNo)) {
            param.setLicNo(licNo);
        }

        return yhytLicenseService.getOne(Condition.getQueryWrapper(param));
    }

    /**
     * 获取无证零售户信息
     */
    private YhytLicenseUnlicensedEntity fetchUnlicensedEntity(String yhytId, String licNo) {
        if (StrUtil.isBlank(yhytId) && StrUtil.isBlank(licNo)) {
            return null;
        }

        YhytLicenseUnlicensedEntity param = new YhytLicenseUnlicensedEntity();
        if(StrUtil.isNotBlank(yhytId)) {
            param.setId(Long.parseLong(yhytId));
        }
        if(StrUtil.isNotBlank(licNo)) {
            param.setLicNo(licNo);
        }

        return yhytLicenseUnlicensedService.getOne(Condition.getQueryWrapper(param));
    }

    @Override
    public IPage<DingMapLicenseVO> selectYhytPage(String searchParam, String formatParam, IPage<YhytLicenseVO> page, BigDecimal longitude,BigDecimal latitude) {

        YhytLicenseVO param = new YhytLicenseVO();
        if (StrUtil.isNotBlank(searchParam)) {
            // 判断searchParam内容是否为纯数字
            if (searchParam.matches("^[0-9]+$")) {
                param.setLicNo(searchParam);
            } else {
                param.setCompanyName(searchParam);
            }
        }

        if (StrUtil.isNotBlank(formatParam)) {
            if(!(formatParam.equals("全部"))){
                param.setBizFormat(formatParam);
            }
        }
        if (longitude != null && latitude != null) {
            param.setLatitude(latitude);
            param.setLongitude(longitude);
        }

        IPage<YhytLicenseVO> yhytLicenseVOIPage = yhytLicenseService.selectYhytLicensePage(page, param);
        // 处理数据
        IPage<DingMapLicenseVO> dingPage = new Page<>();
        dingPage.setCurrent(yhytLicenseVOIPage.getCurrent());
        dingPage.setSize(yhytLicenseVOIPage.getSize());
        dingPage.setTotal(yhytLicenseVOIPage.getTotal());

        List<Long> ids = yhytLicenseVOIPage.getRecords().stream()
                .map(YhytLicenseVO::getId)
                .toList();

        // 批量查询所有需要的门面照片
        List<UploadFileEntity> allCenterPhotos = uploadFileService.selectCenterPhotoFileList(ids);

        List<DingMapLicenseVO> dingMapLicenseVOList = new ArrayList<>();
        // 创建一个 Map，以 objId 为 key，存储对应的照片列表，方便后续查找
        Map<Long, List<UploadFileEntity>> photoMap = allCenterPhotos.stream()
                .collect(Collectors.groupingBy(UploadFileEntity::getObjId));

        for (YhytLicenseVO entity : yhytLicenseVOIPage.getRecords()) {
            DingMapLicenseVO dingMapLicenseVO = new DingMapLicenseVO(entity);
            // 从 Map 中根据 id 获取对应的照片列表
            List<UploadFileEntity> fileList = photoMap.getOrDefault(dingMapLicenseVO.getId(), Collections.emptyList());
            dingMapLicenseVO.setPhotoPathList(fileList);
            dingMapLicenseVOList.add(dingMapLicenseVO);
        }

        dingPage.setRecords(dingMapLicenseVOList);
        return dingPage;
    }


}
