package org.springblade.ms.dingapp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.ms.dingapp.service.IDingReportComplaintService;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-02-16 18:39
 */
@Service
@AllArgsConstructor
public class DingReportComplaintServiceImpl implements IDingReportComplaintService {

    private final IReportComplaintService reportComplaintService;


    @Override
    public IPage<ReportComplaintVO> selectPage(IPage<ReportComplaintEntity> page, Long userId) {
        QueryWrapper<ReportComplaintEntity> queryWrapper = new QueryWrapper<>();
        if (userId != null) {
            queryWrapper.eq("user_id", userId);
        }
        IPage<ReportComplaintEntity> page1 = reportComplaintService.page(page, queryWrapper);
        IPage<ReportComplaintVO> voPage = page1.convert(entity -> BeanUtil.copyProperties(entity, ReportComplaintVO.class));

        return voPage;
    }

    @Override
    public IPage<ReportComplaintVO> selectPageByUserId(IPage<ReportComplaintEntity> page, Long userId) {
        QueryWrapper<ReportComplaintEntity> queryWrapper = new QueryWrapper<>();
        if (userId != null) {
            queryWrapper.eq("user_id", userId);
        }
        IPage<ReportComplaintEntity> page1 = reportComplaintService.page(page, queryWrapper);
        IPage<ReportComplaintVO> voPage = page1.convert(entity -> BeanUtil.copyProperties(entity, ReportComplaintVO.class));

        return voPage;
    }
}
