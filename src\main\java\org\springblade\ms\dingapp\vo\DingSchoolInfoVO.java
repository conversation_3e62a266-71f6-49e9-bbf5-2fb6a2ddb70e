package org.springblade.ms.dingapp.vo;

import lombok.Data;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.ms.dingapp.utils.CoordinateTransformUtil;
import org.springblade.ms.schoolInfo.pojo.entity.MsSchoolInfo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 地图学校信息对象
 * <AUTHOR> @date 2025-03-20
 */
@Data
public class DingSchoolInfoVO extends MsSchoolInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 从MsSchoolInfo构造，并转换坐标系
     */
    public DingSchoolInfoVO(MsSchoolInfo entity) {
        if (entity != null) {
            BeanUtil.copyProperties(entity,this);
            
            // 转换高德坐标系(GCJ-02)到天地图坐标系(CGCS2000)
            if (entity.getLatitude() != null && entity.getLongitude() != null) {
                BigDecimal[] coordinates = CoordinateTransformUtil.gcj02ToCgcs2000(entity.getLatitude(), entity.getLongitude());
                this.setLatitude(coordinates[0]);
                this.setLongitude(coordinates[1]);
            }
        }
    }
}
