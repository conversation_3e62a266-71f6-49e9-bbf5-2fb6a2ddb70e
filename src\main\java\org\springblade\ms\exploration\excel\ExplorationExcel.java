/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.ms.exploration.excel;


import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 勘查记录 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ExplorationExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Long id;
	/**
	 * 零售户 ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("零售户 ID")
	private Long licenseId;
	/**
	 * 勘查日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("勘查日期")
	private Date explorationDate;
	/**
	 * 创建方式（拍照、搜索）
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建方式（拍照、搜索）")
	private String createType;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 是否已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否已删除")
	private Integer isDeleted;
	/**
	 * 经度（最新）
	 */
	@ColumnWidth(20)
	@ExcelProperty("经度（最新）")
	private BigDecimal longitude;
	/**
	 * 纬度（最新）
	 */
	@ColumnWidth(20)
	@ExcelProperty("纬度（最新）")
	private BigDecimal latitude;

}
