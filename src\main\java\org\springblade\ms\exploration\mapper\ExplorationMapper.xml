<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.exploration.mapper.ExplorationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="explorationResultMap" type="org.springblade.ms.exploration.pojo.entity.ExplorationEntity">
        <result column="id" property="id"/>
        <result column="license_id" property="licenseId"/>
        <result column="exploration_date" property="explorationDate"/>
        <result column="create_type" property="createType"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
    </resultMap>

    <resultMap id="explorationVOResultMap" type="org.springblade.ms.exploration.pojo.vo.ExplorationVO" extends="explorationResultMap"/>

    <select id="selectExplorationPage" resultMap="explorationVOResultMap">
        select * from ms_exploration where is_deleted = 0
    </select>

    <select id="getExplorationByParam" resultMap="explorationVOResultMap">
        select *
        from ms_exploration
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="param.licenseId != null">
                and license_id = #{param.licenseId}
            </if>
            <if test="param.explorationDate != null">
                and date(exploration_date) = to_date(#{param.explorationDate}, 'YYYY-MM-DD')
            </if>
        </where>
    </select>
</mapper>
