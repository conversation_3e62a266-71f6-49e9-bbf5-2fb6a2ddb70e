<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.ms.itemidentify.mapper.ItemIdentifyResultsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="itemIdentifyResultsResultMap" type="org.springblade.ms.itemidentify.pojo.entity.ItemIdentifyResultsEntity">
        <result column="id" property="id"/>
        <result column="item_name" property="itemName"/>
        <result column="item_code" property="itemCode"/>
        <result column="accuracy" property="accuracy"/>
        <result column="quantity" property="quantity"/>
        <result column="collision_type" property="collisionType"/>
        <result column="identity_date" property="identifyDate"/>
        <result column="exploration_id" property="explorationId"/>
        <result column="identity_id" property="identifyId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectItemIdentifyResultsPage" resultMap="itemIdentifyResultsResultMap">
        select * from ms_item_identify_results where is_deleted = 0
    </select>


    <select id="exportItemIdentifyResults" resultType="org.springblade.ms.itemidentify.excel.ItemIdentifyResultsExcel">
        SELECT * FROM ms_item_identify_results ${ew.customSqlSegment}
    </select>


    <select id="listAll" resultType="org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO">
        SELECT item_code,
        MAX(item_name) AS item_name,
        MAX(quantity) AS quantity,
        MAX(collision_type) AS collision_type,
        MAX(identify_date) AS identify_date,
        MAX(exploration_id) AS exploration_id,
        MAX(identify_id) AS identify_id,
        MAX(create_time) AS create_time
        FROM ms_item_identify_results
        <where>
            <if test="true">
                is_deleted = 0
            </if>
            <if test="entity.explorationId != null">
                and exploration_id = #{entity.explorationId}
            </if>
            <if test="entity.identifyId != null">
                and identify_id = #{entity.identifyId}
            </if>
            <if test="entity.identifyDate != null">
                and identify_date = #{entity.identifyDate}
            </if>
            <if test="entity.startTime != null">
                AND create_time >= #{entity.startTime}
            </if>
            <if test="entity.endTime != null">
                AND create_time &lt; #{entity.endTime}
            </if>
        </where>
        GROUP BY item_code
        order by item_code
    </select>

    <select id="countListGroupByTimeSegment" resultType="org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsVO">
        SELECT
            identify_date,
            exploration_id,
            CASE
                WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 0 THEN '00:00-02:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 2 THEN '02:00-04:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 4 THEN '04:00-06:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 6 THEN '06:00-08:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 8 THEN '08:00-10:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 10 THEN '10:00-12:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 12 THEN '12:00-14:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 14 THEN '14:00-16:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 16 THEN '16:00-18:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 18 THEN '18:00-20:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 20 THEN '20:00-22:00'
            WHEN EXTRACT(HOUR FROM ms_item_identify_results.create_time)::integer / 2 * 2 = 22 THEN '22:00-24:00'
            ELSE 'Unknown'
        END AS time_segment,
        COUNT(*) AS identify_num,
        COUNT(CASE WHEN ms_item_identify_results.collision_type = '非烟' THEN 1 END) AS error_num
    FROM
        ms_item_identify_results
    WHERE
        ms_item_identify_results.is_deleted = 0
        and exploration_id IN (
            SELECT id
            FROM ms_exploration
            WHERE license_id = #{item.licId}
        )
        <if test="item.createUser != null">
            AND ms_item_identify_results.create_user = #{item.createUser}
        </if>
        GROUP BY
        identify_date,
        exploration_id,
        time_segment
        ORDER BY
        identify_date DESC,
        time_segment
    </select>

    <select id="listAllWithRetailerInfo" resultType="org.springblade.ms.itemidentify.pojo.vo.ItemIdentifyResultsWithRetailerVO">
        SELECT
            mir.identify_date,
            mir.exploration_id,
            CASE
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 0 THEN '00:00-02:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 2 THEN '02:00-04:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 4 THEN '04:00-06:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 6 THEN '06:00-08:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 8 THEN '08:00-10:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 10 THEN '10:00-12:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 12 THEN '12:00-14:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 14 THEN '14:00-16:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 16 THEN '16:00-18:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 18 THEN '18:00-20:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 20 THEN '20:00-22:00'
                WHEN EXTRACT(HOUR FROM mir.create_time)::integer / 2 * 2 = 22 THEN '22:00-24:00'
                ELSE 'Unknown'
            END AS time_segment,
            COUNT(*) AS identify_num,
            COUNT(CASE WHEN mir.collision_type = '非烟' THEN 1 END) AS error_num,
            MAX(me.license_id) AS license_id,
            MAX(myl.company_name) AS retailer_name,
            MAX(myl.business_addr) AS retailer_address,
            MAX(myl.lic_no) AS retailer_lic_no,
            MAX(bu.real_name) AS creator_real_name
        FROM
            ms_item_identify_results mir
        LEFT JOIN
            ms_exploration me ON mir.exploration_id = me.id
        LEFT JOIN
            ms_yhyt_license myl ON me.license_id = myl.id
        LEFT JOIN
            blade_user bu ON mir.create_user = bu.id
        <where>
            <if test="true">
                mir.is_deleted = 0
            </if>
            <if test="item.createUser != null">
                AND mir.create_user = #{item.createUser}
            </if>
            <if test="item.identifyDate != null">
                AND mir.identify_date = #{item.identifyDate}
            </if>
            <if test="item.explorationId != null">
                AND mir.exploration_id = #{item.explorationId}
            </if>
            <if test="item.identifyId != null">
                AND mir.identify_id = #{item.identifyId}
            </if>
            <if test="item.startTime != null">
                AND mir.create_time >= #{item.startTime}
            </if>
            <if test="item.endTime != null">
                AND mir.create_time &lt; #{item.endTime}
            </if>
        </where>
        GROUP BY
            mir.identify_date,
            mir.exploration_id,
            time_segment
        ORDER BY
            mir.identify_date DESC,
            time_segment
    </select>
</mapper>
