package org.springblade.ms.itemidentify.pojo.entity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 近半年零售户订货品规
 * @TableName ms_recent_retailer_order_product
 */
@TableName(value ="ms_recent_retailer_order_product")
@Data
public class MsRecentRetailerOrderProduct extends TenantEntity implements Serializable {
    /**
     * 客户标识
     */
    @TableField(value = "customer_uuid")
    private String customerUuid;

    /**
     * 客户编码
     */
    @TableField(value = "customer_code")
    private String customerCode;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 商品标识
     */
    @TableField(value = "product_uuid")
    private String productUuid;

    /**
     * 商品编码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 商品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 订单数量
     */
    @TableField(value = "qty")
    private Integer qty;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}