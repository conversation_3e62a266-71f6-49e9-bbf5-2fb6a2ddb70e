package org.springblade.ms.platform12345.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springblade.core.tool.api.R;
import org.springblade.ms.platform12345.config.Platform12345Properties;
import org.springblade.ms.platform12345.dto.*;
import org.springblade.ms.platform12345.dto.QueryCurStateRequest;
import org.springblade.ms.platform12345.service.Platform12345DataService;
import org.springblade.ms.platform12345.service.Platform12345Service;
import org.springblade.ms.platform12345.utils.Platform12345Client;
import org.springframework.web.bind.annotation.*;

/**
 * 12345平台接口控制器
 */
@RestController
@RequestMapping("/platform12345")
@RequiredArgsConstructor
@Tag(name = "12345平台接口")
@Slf4j
public class Platform12345ClientController {

    private final Platform12345Service platform12345Service;
    private final Platform12345Client platform12345Client;
    private final Platform12345Properties properties;
    private final Platform12345DataService platform12345DataService;

    /**
     * 查主办工单 5.1
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/queryMajorOrder")
    @Operation(summary = "查询主办工单", description = "查询12345平台主办的待办工单")
    public JSONObject queryMajorOrder(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        try {
            log.info("查询12345平台主办工单，开始时间：{}，结束时间：{}", startTime, endTime);

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QueryMajorOrderRequest request = new QueryMajorOrderRequest();
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 调用服务
            String result = platform12345Service.queryMajorOrder(request);

            log.info("查询12345平台主办工单成功---"+result);
            String[] districtsAndCities = {"赤坎区", "霞山区", "坡头区", "麻章区", "吴川市", "廉江市", "雷州市", "徐闻县", "遂溪县"};


//            // 同步保存工单数据
//            try {
//                R saveResult = platform12345DataService.saveProWoList(result);
//                log.info("保存工单数据结果: {}", saveResult.isSuccess() ? "成功" : "失败");
//            } catch (Exception e) {
//                log.error("保存工单数据失败", e);
//                // 保存失败不影响查询结果的返回
//            }

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("查询12345平台主办工单失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询12345平台主办工单失败: " + e.getMessage());
        }
    }

    /**
     * 查询数据字典  5.11 未开通权限
     *
     * @param paramTypeCode 数据字典类型
     * @param paramCode 数据字典编码，可选
     * @return 数据字典数据
     */
    @GetMapping("/queryDataDict")
    @Operation(summary = "查询数据字典", description = "根据字典类型获取对应数据字典数据集")
    public JSONObject queryDataDict(@RequestParam("paramTypeCode") String paramTypeCode,
                                   @RequestParam(value = "paramCode", required = false) String paramCode) {
        try {
            log.info("查询数据字典，参数类型：{}，参数编码：{}", paramTypeCode, paramCode);

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QueryDataDictRequest request = new QueryDataDictRequest();
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());
            request.setParamTypeCode(paramTypeCode);
            request.setParamCode(paramCode);

            // 调用服务
            String result = platform12345Service.queryDataDict(request);
            log.info("查询数据字典成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("查询数据字典失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询数据字典失败: " + e.getMessage());
        }
    }

    /**
     * 查询共享工单 7.1
     *
     * @param startTime 开始时间，格式：YYYY-MM-DD HH24:MI:SS
     * @param endTime 结束时间，格式：YYYY-MM-DD HH24:MI:SS
     * @return 共享工单数据
     */
    @GetMapping("/queryShareOrder")
    @Operation(summary = "查询共享工单", description = "查询热线平台已办结并且共享给各个部门的工单")
    public JSONObject queryShareOrder(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        try {
            log.info("查询共享工单，开始时间：{}，结束时间：{}", startTime, endTime);

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QueryShareOrderRequest request = new QueryShareOrderRequest();
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 调用服务
            String result = platform12345Service.queryShareOrder(request);
            log.info("查询共享工单成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("查询共享工单失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询共享工单失败: " + e.getMessage());
        }
    }

    /**
     * 6.3提交工单处理反馈接口（受理后退回审核工单）
     *
     * @param dealOrderDTO 处理反馈工单DTO
     * @return 处理结果
     */
    @PostMapping("/dealOrder")
    @Operation(summary = "处理反馈工单", description = "对工单进行处理数据同步操作")
    public JSONObject dealOrder(@RequestBody DealOrderDTO dealOrderDTO) {
        try {
            log.info("处理反馈工单，工单ID：{}，工单编号：{}", dealOrderDTO.getProWoId(), dealOrderDTO.getProWoCode());

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            DealOrderRequest request = new DealOrderRequest();
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());
            request.setDealUserNo(properties.getDealUserNo());

            request.setProWoId(dealOrderDTO.getProWoId());
            request.setProWoCode(dealOrderDTO.getProWoCode());
            request.setDealContent(dealOrderDTO.getDealContent());
            request.setDealTime(dealOrderDTO.getDealTime());
            request.setDealOrgName(dealOrderDTO.getDealOrgName());
//            request.setDealOrgCode(dealOrderDTO.getDealOrgCode());

            // 调用服务
            String result = platform12345Service.dealOrder(request);
            log.info("处理反馈工单成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("处理反馈工单失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "处理反馈工单失败: " + e.getMessage());
        }
    }

    /**
     * 申请延期接口 6.1
     *
     * @param applyDelayDTO 延期申请DTO
     * @return 处理结果
     */
    @PostMapping("/applyDelay")
    @Operation(summary = "申请延期", description = "对工单进行延期申请操作")
    public JSONObject applyDelay(@RequestBody ApplyDelayDTO applyDelayDTO) {
        try {
            log.info("申请延期，工单ID：{}，工单编号：{}", applyDelayDTO.getProWoId(), applyDelayDTO.getProWoCode());

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            ApplyDelayRequest request = new ApplyDelayRequest();
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 设置必填参数
            request.setProWoId(applyDelayDTO.getProWoId());
            request.setProWoCode(applyDelayDTO.getProWoCode());
            request.setContent(applyDelayDTO.getContent());
            request.setApplyTime(applyDelayDTO.getApplyTime());
            request.setMaxFldelay(applyDelayDTO.getMaxFldelay());
            request.setDateType(applyDelayDTO.getDateType());

            // 设置可选参数
            request.setApplyUserNo(properties.getDealUserNo());
            request.setApplyOrgName(applyDelayDTO.getApplyOrgName());

            // 调用服务
            String result = platform12345Service.applyDelay(request);
            log.info("申请延期成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("申请延期失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "申请延期失败: " + e.getMessage());
        }
    }

    /**
     * 提交受理接口  6.6
     *
     * @param acceptOrderDTO 提交受理DTO
     * @return 处理结果
     */
    @PostMapping("/acceptOrder")
    @Operation(summary = "提交受理", description = "对工单进行提交受理操作")
    public JSONObject acceptOrder(@RequestBody AcceptOrderDTO acceptOrderDTO) {
        try {
            log.info("提交受理，工单ID：{}，工单编号：{}", acceptOrderDTO.getProWoId(), acceptOrderDTO.getProWoCode());

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            AcceptOrderRequest request = new AcceptOrderRequest();
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 设置必填参数
            request.setProWoId(acceptOrderDTO.getProWoId());
            request.setProWoCode(acceptOrderDTO.getProWoCode());
            request.setAssignOrgName(acceptOrderDTO.getAssignOrgName());
            request.setAppContent(acceptOrderDTO.getAppContent());
            request.setAppTime(acceptOrderDTO.getAppTime());
            request.setDealUserNo(properties.getDealUserNo());

            // 调用服务
            String result = platform12345Service.acceptOrder(request);
            log.info("提交受理成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("提交受理失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "提交受理失败: " + e.getMessage());
        }
    }

    /**
     * 工单处理完成接口  （6.4办结）
     *
     * @param completeOrderDTO 工单处理完成DTO
     * @return 处理结果
     */
    @PostMapping("/completeOrder")
    @Operation(summary = "工单处理完成", description = "对工单进行处理完成操作")
    public JSONObject completeOrder(@RequestBody CompleteOrderDTO completeOrderDTO) {
        try {
            log.info("工单处理完成，工单ID：{}，工单编号：{}", completeOrderDTO.getProWoId(), completeOrderDTO.getProWoCode());

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            CompleteOrderRequest request = new CompleteOrderRequest();
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 设置必填参数
            request.setProWoId(completeOrderDTO.getProWoId());
            request.setProWoCode(completeOrderDTO.getProWoCode());
            request.setReplyDetails(completeOrderDTO.getReplyDetails());
            if(StrUtil.isNotEmpty(completeOrderDTO.getContent())){
                request.setContent(completeOrderDTO.getContent());
            }
            request.setSortManage(completeOrderDTO.getSortManage());
            request.setIsAccept(completeOrderDTO.getIsAccept());
            request.setDealTime(completeOrderDTO.getDealTime());
            request.setDealUserNo(properties.getDealUserNo());

            // 设置非必填参数（市场主体工单或者省网渠道工单必填）
            if(StrUtil.isNotEmpty(completeOrderDTO.getAccpetDate())){
                request.setAccpetDate(completeOrderDTO.getAccpetDate());
            }
            if(StrUtil.isNotEmpty(completeOrderDTO.getPublicFeedback())){
                request.setPublicFeedback(completeOrderDTO.getPublicFeedback());
            }
            if(StrUtil.isNotEmpty(completeOrderDTO.getLawGist())){
                request.setLawGist(completeOrderDTO.getLawGist());
            }
            if(StrUtil.isNotEmpty(completeOrderDTO.getEvaluateResults())){
                request.setEvaluateResults(completeOrderDTO.getEvaluateResults());
            }
            if(StrUtil.isNotEmpty(completeOrderDTO.getContactPhone())){
                request.setContactPhone(completeOrderDTO.getContactPhone());
            }

            // 调用服务
            String result = platform12345Service.completeOrder(request);
            log.info("工单处理完成成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("工单处理完成失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "工单处理完成失败: " + e.getMessage());
        }
    }

    /**
     * 查询办结审批结果接口 5.9
     *
     * @param queryReCheckResultDTO 查询工单复核结果DTO
     * @return 复核结果数据
     */
    @PostMapping("/queryReCheckResult")
    @Operation(summary = "查询工单复核结果", description = "查询工单复核结果")
    public JSONObject queryReCheckResult(@RequestBody QueryReCheckResultDTO queryReCheckResultDTO) {
        try {
            log.info("查询工单复核结果，开始时间：{}，结束时间：{}", queryReCheckResultDTO.getStartTime(), queryReCheckResultDTO.getEndTime());

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QueryReCheckResultRequest request = new QueryReCheckResultRequest();
            request.setStartTime(queryReCheckResultDTO.getStartTime());
            request.setEndTime(queryReCheckResultDTO.getEndTime());
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());
            request.setProWoCodes(queryReCheckResultDTO.getProWoCodes());

            // 调用服务
            String result = platform12345Service.queryReCheckResult(request);
            log.info("查询工单复核结果成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("查询工单复核结果失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询工单复核结果失败: " + e.getMessage());
        }
    }

    /**
     * 查询协办工单接口
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 协办工单数据
     */
    @GetMapping("/queryAssistOrder")
    @Operation(summary = "查询协办工单", description = "查询12345平台协办的待办工单")
    public JSONObject queryAssistOrder(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        try {
            log.info("查询协办工单，开始时间：{}，结束时间：{}", startTime, endTime);

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QueryAssistOrderRequest request = new QueryAssistOrderRequest();
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 调用服务
            String result = platform12345Service.queryAssistOrder(request);
            log.info("查询协办工单成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("查询协办工单失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询协办工单失败: " + e.getMessage());
        }
    }

    /**
     * 查询督办工单接口
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 督办工单数据
     */
    @GetMapping("/querySuperviseOrder")
    @Operation(summary = "查询督办工单", description = "查询12345平台待处理的督办工单")
    public JSONObject querySuperviseOrder(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        try {
            log.info("查询督办工单，开始时间：{}，结束时间：{}", startTime, endTime);

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QuerySuperviseOrderRequest request = new QuerySuperviseOrderRequest();
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 调用服务
            String result = platform12345Service.querySuperviseOrder(request);
            log.info("查询督办工单成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("查询督办工单失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询督办工单失败: " + e.getMessage());
        }
    }

    /**
     * 查询工单满意度接口
     *
     * @param queryCurStateDTO 查询工单满意度DTO
     * @return 工单满意度数据
     */
    @PostMapping("/queryCurState")
    @Operation(summary = "查询工单满意度", description = "查询12345平台工单满意度")
    public JSONObject queryCurState(@RequestBody QueryCurStateDTO queryCurStateDTO) {
        try {
            log.info("查询工单满意度，工单编号列表：{}", queryCurStateDTO.getProWoCodes());

            // 生成签名信息
            Platform12345Client.SignatureInfo signatureInfo = platform12345Client.generateSignature();

            // 构建请求
            QueryCurStateRequest request = new QueryCurStateRequest();
            request.setProWoCodes(queryCurStateDTO.getProWoCodes());
            request.setSignature(signatureInfo.getSignature());
            request.setTimestamp(signatureInfo.getTimestamp());
            request.setAppid(signatureInfo.getAppid());

            // 调用服务
            String result = platform12345Service.queryCurState(request);
            log.info("查询工单满意度成功---"+result);

            return JSONUtil.createObj()
                .set("code", 200)
                .set("msg", "操作成功")
                .set("data", JSONUtil.parseObj(result));
        } catch (Exception e) {
            log.error("查询工单满意度失败", e);

            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询工单满意度失败: " + e.getMessage());
        }
    }

}
