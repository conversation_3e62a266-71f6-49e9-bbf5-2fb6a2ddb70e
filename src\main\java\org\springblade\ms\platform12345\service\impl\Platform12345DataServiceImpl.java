package org.springblade.ms.platform12345.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.ms.platform12345.enums.RollTypeEnum;
import org.springblade.ms.platform12345.service.Platform12345DataService;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 12345平台数据处理服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Platform12345DataServiceImpl implements Platform12345DataService {

    private final IReportComplaintService reportComplaintService;

    @Override
    public R saveProWoList(String jsonData) {
        try {
            log.info("开始保存12345平台工单数据");

            // 解析JSON数据
            JSONObject jsonObject = JSONUtil.parseObj(jsonData);
            if (!"0".equals(jsonObject.getStr("resultCode"))) {
                return R.fail("获取数据失败：" + jsonObject.getStr("resultMsg"));
            }

            JSONArray proWoList = jsonObject.getJSONArray("proWoList");
            if (proWoList == null || proWoList.isEmpty()) {
                return R.fail("没有工单数据");
            }

            List<ReportComplaintEntity> saveList = new ArrayList<>();
            for (int i = 0; i < proWoList.size(); i++) {
                JSONObject proWo = proWoList.getJSONObject(i);

                // 使用proWoCode作为id，转换为Long类型
                String proWoCodeStr = proWo.getStr("proWoCode");
                ReportComplaintEntity reportComplaintEntity = new ReportComplaintEntity();
                if (StrUtil.isNotBlank(proWoCodeStr)) {
                    // 去除可能存在的空格，并转换为Long类型
                    String cleanCode = proWoCodeStr.trim();
                    // 如果包含非数字字符（如连字符或其他分隔符），则提取数字部分
                    cleanCode = cleanCode.replaceAll("[^0-9]", "");
                    try {
                        Long proWoCodeLong = Long.parseLong(cleanCode);
                        // 设置为Long类型的id
                        reportComplaintEntity.setId(proWoCodeLong);
                    } catch (NumberFormatException e) {
                        log.warn("无法将proWoCode转换为Long类型: {}", proWoCodeStr);
                        // 如果转换失败，仍然保存原始字符串作为回退方案
                        reportComplaintEntity.setRollNumber(proWoCodeStr);
                        continue; // 跳过此记录，不添加到saveList中
                    }
                } else {
                    // 如果proWoCode为空，跳过此记录
                    continue;
                }

                reportComplaintEntity.setEventTitle(proWo.getStr("title"));
                reportComplaintEntity.setRollNumber(proWo.getStr("proWoId"));


                reportComplaintEntity.setEventContent(proWo.getStr("content"));
                reportComplaintEntity.setHandleRelExpireTime(proWo.getStr("handleTime"));

                reportComplaintEntity.setRollWay(proWo.getStr("callType"));//字典A5.1
                reportComplaintEntity.setRollType(RollTypeEnum.getValueByCode(proWo.getStr("type")));//字典A5.4
                reportComplaintEntity.setRollStatus(proWo.getStr("state"));//字典A5.2

                reportComplaintEntity.setEventType(proWo.getStr("actName"));//？
                reportComplaintEntity.setCityOrgName(proWo.getStr("areaCode"));//code占用了name？
                reportComplaintEntity.setCityOrgCode(proWo.getStr("actOrgCode"));//无？
                reportComplaintEntity.setEventAddr(proWo.getStr("address"));//待确定
                reportComplaintEntity.setEventRegion(proWo.getStr("actArea"));
                reportComplaintEntity.setEventTarget(proWo.getStr("thingSubject"));
                reportComplaintEntity.setComplaintStatus("待处理");
                // 处理日期时间
                if (StrUtil.isNotBlank(proWo.getStr("createTime"))) {
                    reportComplaintEntity.setRollTime(DateUtil.parse(proWo.getStr("createTime"), DatePattern.NORM_DATETIME_FORMAT));
                    reportComplaintEntity.setRollDate(DateUtil.format(DateUtil.parse(proWo.getStr("createTime"), DatePattern.NORM_DATETIME_FORMAT), DatePattern.NORM_DATE_FORMAT));
                }
                saveList.add(reportComplaintEntity);
            }

            // 批量保存或更新
            boolean result = reportComplaintService.saveOrUpdateBatch(saveList);

            return R.status(result).data(saveList.size());
        } catch (Exception e) {
            log.error("保存工单数据失败", e);
            return R.fail("保存工单数据失败：" + e.getMessage());
        }
    }


}
