package org.springblade.ms.platform12345.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springblade.ms.platform12345.config.Platform12345Properties;
import org.springblade.ms.platform12345.dto.*;
import org.springblade.ms.platform12345.service.Platform12345Service;

import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 12345平台服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Platform12345ServiceImpl implements Platform12345Service {

    private final Platform12345Properties properties;

    @Override
    public String queryMajorOrder(QueryMajorOrderRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/proWoProcess/queryMajorOrder";
            log.info("请求url："+url);
            // 发送请求
            return sendRequest(url, request);
        } catch (Exception e) {
            log.error("查询主办工单失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询主办工单失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    @Override
    public String queryDataDict(QueryDataDictRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/proWoProcess/queryDataDict";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForDataDict(url, request);
        } catch (Exception e) {
            log.error("查询数据字典失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询数据字典失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求 - 查询主办工单
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequest(String url, QueryMajorOrderRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("startTime", request.getStartTime())
                .set("endTime", request.getEndTime())
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid());

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 发送HTTP请求 - 查询数据字典
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    /**
     * 查询共享工单
     *
     * @param request 请求参数
     * @return 共享工单数据
     */
    @Override
    public String queryShareOrder(QueryShareOrderRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/proShareData/queryShareOrder";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForShareOrder(url, request);
        } catch (Exception e) {
            log.error("查询共享工单失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询共享工单失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求 - 查询数据字典
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForDataDict(String url, QueryDataDictRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid())
                .set("paramTypeCode", request.getParamTypeCode());

            // 如果paramCode不为空，则添加到请求中
            if (request.getParamCode() != null && !request.getParamCode().trim().isEmpty()) {
                requestJson.set("paramCode", request.getParamCode());
            }

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 发送HTTP请求 - 查询共享工单
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    /**
     * 处理反馈工单
     *
     * @param request 请求参数
     * @return 处理结果
     */
    @Override
    public String dealOrder(DealOrderRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/handlerProcess/dealOrder";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForDealOrder(url, request);
        } catch (Exception e) {
            log.error("处理反馈工单失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "处理反馈工单失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求 - 查询共享工单
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForShareOrder(String url, QueryShareOrderRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("startTime", request.getStartTime())
                .set("endTime", request.getEndTime())
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid());

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 发送HTTP请求 - 处理反馈工单
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForDealOrder(String url, DealOrderRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid())
                .set("proWoId", request.getProWoId())
                .set("proWoCode", request.getProWoCode())
                .set("dealContent", request.getDealContent())
                .set("dealTime", request.getDealTime())
                .set("dealOrgName", request.getDealOrgName())
                .set("dealUserNo", request.getDealUserNo());

            // 如果dealOrgCode不为空，则添加到请求中
            if (request.getDealOrgCode() != null && !request.getDealOrgCode().trim().isEmpty()) {
                requestJson.set("dealOrgCode", request.getDealOrgCode());
            }

            // 不添加附件数据
            requestJson.set("fileDatas", new Object[0]);

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 申请延期
     *
     * @param request 请求参数
     * @return 处理结果
     */
    @Override
    public String applyDelay(ApplyDelayRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/handlerProcess/applyDelay";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForApplyDelay(url, request);
        } catch (Exception e) {
            log.error("申请延期失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "申请延期失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 提交受理
     *
     * @param request 请求参数
     * @return 处理结果
     */
    @Override
    public String acceptOrder(AcceptOrderRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/handlerProcess/acceptOrder";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForAcceptOrder(url, request);
        } catch (Exception e) {
            log.error("提交受理失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "提交受理失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求 - 申请延期
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForApplyDelay(String url, ApplyDelayRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid())
                .set("proWoId", request.getProWoId())
                .set("proWoCode", request.getProWoCode())
                .set("content", request.getContent())
                .set("applyTime", request.getApplyTime())
                .set("maxFldelay", request.getMaxFldelay())
                .set("dateType", request.getDateType());

            // 如果applyUserNo不为空，则添加到请求中
            if (StrUtil.isNotBlank(request.getApplyUserNo())) {
                requestJson.set("applyUserNo", request.getApplyUserNo());
            }

            // 如果applyOrgName不为空，则添加到请求中
            if (StrUtil.isNotBlank(request.getApplyOrgName())) {
                requestJson.set("applyOrgName", request.getApplyOrgName());
            }

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 发送HTTP请求 - 提交受理
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForAcceptOrder(String url, AcceptOrderRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid())
                .set("proWoId", request.getProWoId())
                .set("proWoCode", request.getProWoCode())
                .set("assignOrgName", request.getAssignOrgName())
                .set("appContent", request.getAppContent())
                .set("appTime", request.getAppTime())
                .set("dealUserNo", request.getDealUserNo());

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 工单处理完成
     *
     * @param request 请求参数
     * @return 处理结果
     */
    @Override
    public String completeOrder(CompleteOrderRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/handlerProcess/auditOrder";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForCompleteOrder(url, request);
        } catch (Exception e) {
            log.error("工单处理完成失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "工单处理完成失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求 - 工单处理完成
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForCompleteOrder(String url, CompleteOrderRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid())
                .set("proWoId", request.getProWoId())
                .set("proWoCode", request.getProWoCode())
                .set("replyDetails", request.getReplyDetails())
                .set("sortManage", request.getSortManage())
                .set("isAccept", request.getIsAccept())
                .set("dealTime", request.getDealTime())
                .set("dealUserNo", request.getDealUserNo());

            // 添加可选参数
            if (StrUtil.isNotBlank(request.getContent())) {
                requestJson.set("content", request.getContent());
            }
            if (StrUtil.isNotBlank(request.getIsWrittenReply())) {
                requestJson.set("isWrittenReply", request.getIsWrittenReply());
            }
            if (StrUtil.isNotBlank(request.getNowrittenReplyReason())) {
                requestJson.set("nowrittenReplyReason", request.getNowrittenReplyReason());
            }
            if (StrUtil.isNotBlank(request.getReplyType())) {
                requestJson.set("replyType", request.getReplyType());
            }
            if (StrUtil.isNotBlank(request.getEmsNo())) {
                requestJson.set("emsNo", request.getEmsNo());
            }
            if (StrUtil.isNotBlank(request.getCheckCase())) {
                requestJson.set("checkCase", request.getCheckCase());
            }
            if (StrUtil.isNotBlank(request.getAccpetDate())) {
                requestJson.set("accpetDate", request.getAccpetDate());
            }
            if (StrUtil.isNotBlank(request.getPublicFeedback())) {
                requestJson.set("publicFeedback", request.getPublicFeedback());
            }
            if (StrUtil.isNotBlank(request.getLawGist())) {
                requestJson.set("lawGist", request.getLawGist());
            }
            if (StrUtil.isNotBlank(request.getEvaluateResults())) {
                requestJson.set("evaluateResults", request.getEvaluateResults());
            }
            if (StrUtil.isNotBlank(request.getContactPhone())) {
                requestJson.set("contactPhone", request.getContactPhone());
            }

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * 查询工单复核结果
     *
     * @param request 请求参数
     * @return 复核结果数据
     */
    @Override
    public String queryReCheckResult(QueryReCheckResultRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/proWoProcess/queryReCheckResult";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForReCheckResult(url, request);
        } catch (Exception e) {
            log.error("查询工单复核结果失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询工单复核结果失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求 - 查询工单复核结果
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForReCheckResult(String url, QueryReCheckResultRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("startTime", request.getStartTime())
                .set("endTime", request.getEndTime())
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid());

            // 如果proWoCodes不为空，则添加到请求中
            if (request.getProWoCodes() != null && !request.getProWoCodes().isEmpty()) {
                requestJson.set("proWoCodes", request.getProWoCodes());
            }

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    @Override
    public String queryAssistOrder(QueryAssistOrderRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/proWoProcess/queryAssistOrder";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForAssistOrder(url, request);
        } catch (Exception e) {
            log.error("查询协办工单失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询协办工单失败: " + e.getMessage())
                .toString();
        }
    }

    private String sendRequestForAssistOrder(String url, QueryAssistOrderRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("startTime", request.getStartTime())
                .set("endTime", request.getEndTime())
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid());

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    @Override
    public String querySuperviseOrder(QuerySuperviseOrderRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/proWoProcess/querySuperviseOrder";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForSuperviseOrder(url, request);
        } catch (Exception e) {
            log.error("查询督办工单失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询督办工单失败: " + e.getMessage())
                .toString();
        }
    }

    private String sendRequestForSuperviseOrder(String url, QuerySuperviseOrderRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("startTime", request.getStartTime())
                .set("endTime", request.getEndTime())
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid());

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }

    @Override
    public String queryCurState(QueryCurStateRequest request) {
        try {
            // 构建请求URL
            String url = properties.getApiRoot() + "/12345_inter/handlerProcess/queryCurState";
            log.info("请求url："+url);
            // 发送请求
            return sendRequestForCurState(url, request);
        } catch (Exception e) {
            log.error("查询工单满意度失败", e);
            // 返回错误信息
            return JSONUtil.createObj()
                .set("code", 500)
                .set("msg", "查询工单满意度失败: " + e.getMessage())
                .toString();
        }
    }

    /**
     * 发送HTTP请求 - 查询工单满意度
     *
     * @param url     请求URL
     * @param request 请求参数
     * @return 响应结果
     */
    private String sendRequestForCurState(String url, QueryCurStateRequest request) throws Exception {
        // 创建支持SSL的HttpClient
        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustAllStrategy());
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
            builder.build(), NoopHostnameVerifier.INSTANCE);

        try (CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(sslsf).build()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestJson = JSONUtil.createObj()
                .set("proWoCodes", request.getProWoCodes())
                .set("signature", request.getSignature())
                .set("timestamp", request.getTimestamp())
                .set("appid", request.getAppid());

            String requestBody = requestJson.toString();

            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            // 发送请求并获取响应
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity, StandardCharsets.UTF_8);
            }
        }
    }
}
