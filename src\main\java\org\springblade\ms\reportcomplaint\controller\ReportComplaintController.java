/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.reportcomplaint.controller;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.ms.reportcomplaint.excel.Import12345Excel;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO;
import org.springblade.ms.reportcomplaint.excel.ReportComplaintExcel;
import org.springblade.ms.reportcomplaint.wrapper.ReportComplaintWrapper;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 工单信息 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-reportcomplaint/reportComplaint")
@Tag(name = "工单信息", description = "工单信息接口")
public class ReportComplaintController extends BladeController {

	private final IReportComplaintService reportComplaintService;

	/**
	 * 工单信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入reportComplaint")
	public R<ReportComplaintVO> detail(ReportComplaintEntity reportComplaint) {
		ReportComplaintEntity detail = reportComplaintService.getOne(Condition.getQueryWrapper(reportComplaint));
		return R.data(ReportComplaintWrapper.build().entityVO(detail));
	}
	/**
	 * 工单信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入reportComplaint")
	public R<IPage<ReportComplaintVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> reportComplaint, Query query) {
		IPage<ReportComplaintEntity> pages = reportComplaintService.page(Condition.getPage(query), Condition.getQueryWrapper(reportComplaint, ReportComplaintEntity.class));
		return R.data(ReportComplaintWrapper.build().pageVO(pages));
	}

	/**
	 * 工单信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入reportComplaint")
	public R<IPage<ReportComplaintVO>> page(ReportComplaintVO reportComplaint, Query query) {
		IPage<ReportComplaintVO> pages = reportComplaintService.selectReportComplaintPage(Condition.getPage(query), reportComplaint);
		return R.data(pages);
	}

	/**
	 * 工单信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入reportComplaint")
	public R save(@Valid @RequestBody ReportComplaintEntity reportComplaint) {
		return R.status(reportComplaintService.save(reportComplaint));
	}

	/**
	 * 工单信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入reportComplaint")
	public R update(@Valid @RequestBody ReportComplaintEntity reportComplaint) {
		return R.status(reportComplaintService.updateById(reportComplaint));
	}

	/**
	 * 工单信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入reportComplaint")
	public R submit(@Valid @RequestBody ReportComplaintEntity reportComplaint) {
		return R.status(reportComplaintService.saveOrUpdate(reportComplaint));
	}

	/**
	 * 工单信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(reportComplaintService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-reportComplaint")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入reportComplaint")
	public void exportReportComplaint(@Parameter(hidden = true) @RequestParam Map<String, Object> reportComplaint, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ReportComplaintEntity> queryWrapper = Condition.getQueryWrapper(reportComplaint, ReportComplaintEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ReportComplaint::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ReportComplaintEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ReportComplaintExcel> list = reportComplaintService.exportReportComplaint(queryWrapper);
		ExcelUtil.export(response, "工单信息数据" + DateUtil.timer(), "工单信息数据表", list, ReportComplaintExcel.class);
	}


	@PostMapping("/import12313")
	public R import12313(MultipartFile file) throws InstantiationException, IllegalAccessException {



		return R.success();


	}


	@PostMapping("/import12345")
	public R import12345(MultipartFile file) throws InstantiationException, IllegalAccessException {
		List<Import12345Excel> list = new ArrayList<>();
		try {
			list = ExcelUtil.read(file, Import12345Excel.class);
		} catch (Exception ex) {
			if (ex instanceof ExcelAnalysisException || ex instanceof ExcelDataConvertException) {
				if (ex.getCause() != null) {
					ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) ex.getCause();
					if (excelDataConvertException != null) {
						if (StrUtil.isNotBlank(excelDataConvertException.getCause().getMessage())) {
							return R.fail(StrUtil.format("第{}行，第{}列解析异常，{}", excelDataConvertException.getRowIndex() + 2,
									excelDataConvertException.getColumnIndex() + 1, excelDataConvertException.getCause().getMessage()));
						} else {
							return R.fail(StrUtil.format("第{}行，第{}列解析异常，数据不能为:{}", excelDataConvertException.getRowIndex() + 2,
									excelDataConvertException.getColumnIndex() + 1, excelDataConvertException.getCellData()));
						}
					}
				}
			}
		}

		if (IterUtil.isEmpty(list)) {
			return R.fail("导入失败，读取到的文件内容为空。");
		}

		List<ReportComplaintEntity> saveList = new ArrayList<>();
		for (Import12345Excel import12345Excel : list) {
			ReportComplaintEntity reportComplaintEntity = new ReportComplaintEntity();

			// 检查受理号是有已经有数据，有就更新，没有就新增
			if (StrUtil.isNotBlank(import12345Excel.getWorkOrderNumber())) {
				ReportComplaintEntity dbReportComplaint = reportComplaintService.getReportComplaintByRollNumber(import12345Excel.getWorkOrderNumber());
				if (dbReportComplaint != null) {
					reportComplaintEntity.setId(dbReportComplaint.getId());
				}
			}

			reportComplaintEntity.setRollNumber(import12345Excel.getWorkOrderNumber());
			reportComplaintEntity.setRollWay("12345");
			reportComplaintEntity.setRollType(import12345Excel.getRollType());
			reportComplaintEntity.setRollTime(DateUtil.parse(import12345Excel.getAcceptanceTime(), DatePattern.NORM_DATETIME_FORMAT));
			reportComplaintEntity.setRollDate(DateUtil.format(DateUtil.parse(import12345Excel.getAcceptanceTime(), DatePattern.NORM_DATETIME_FORMAT), DatePattern.NORM_DATE_FORMAT));

			StringBuilder eventType = new StringBuilder();
			if (StrUtil.isNotBlank(import12345Excel.getCategoryLevelOne())) {
				eventType.append(import12345Excel.getCategoryLevelOne());
			}
			if (StrUtil.isNotBlank(import12345Excel.getCategoryLevelTwo())) {
				eventType.append("/").append(import12345Excel.getCategoryLevelTwo());
			}
			if (StrUtil.isNotBlank(import12345Excel.getCategoryLevelThree())) {
				eventType.append("/").append(import12345Excel.getCategoryLevelThree());
			}
			reportComplaintEntity.setEventType(eventType.toString());

			reportComplaintEntity.setEventAddr(import12345Excel.getIncidentLocation());
			reportComplaintEntity.setEventContent(import12345Excel.getCitizenAppeal());
			reportComplaintEntity.setHandleType(import12345Excel.getWorkOrderStatus());
			reportComplaintEntity.setHandleNoteDate(import12345Excel.getHandleNoteDate());
			reportComplaintEntity.setRollHandleResult(import12345Excel.getReplyContent());

			saveList.add(reportComplaintEntity);
		}

		if (IterUtil.isNotEmpty(saveList)) {
			reportComplaintService.saveOrUpdateBatch(saveList);
		}

		return R.success();

	}



}
