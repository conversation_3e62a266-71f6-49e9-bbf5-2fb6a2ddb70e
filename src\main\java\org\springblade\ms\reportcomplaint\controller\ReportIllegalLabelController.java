package org.springblade.ms.reportcomplaint.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-19 19:07
 */
@RestController
@AllArgsConstructor
@RequestMapping("ms-reportcomplaint/reportIllegalLabel")
public class ReportIllegalLabelController extends BladeController {

    private final IReportIllegalLabelService reportIllegalLabelService;

    /**
     * 工单信息 新增或修改
     */
    @PostMapping("/submit")
    public R submit(
            @RequestParam("objId") Long objId,
            @Valid @RequestBody List<ReportIllegalLabelVO> reportIllegalLabelList
    ) {
        return R.status(reportIllegalLabelService.saveOrUpdate(objId, reportIllegalLabelList));
    }

}
