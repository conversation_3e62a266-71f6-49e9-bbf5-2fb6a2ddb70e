package org.springblade.ms.reportcomplaint.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-16 23:41
 */
public interface IReportIllegalLabelService extends BaseService<ReportIllegalLabelEntity> {

    List<ReportIllegalLabelVO> selectListByObjId(Long objId);

    Boolean saveOrUpdate(Long objId, List<ReportIllegalLabelVO> reportIllegalLabelList);

    Long getIllegalRecordsLastYearCount(Long yhytId);

    List<ReportIllegalLabelVO> listByLabelId(Long labelId) ;
}
