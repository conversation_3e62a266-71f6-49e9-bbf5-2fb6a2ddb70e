/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.ms.reportcomplaint.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.UserCache;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportComplaintEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportComplaintVO;
import org.springblade.ms.reportcomplaint.excel.ReportComplaintExcel;
import org.springblade.ms.reportcomplaint.mapper.ReportComplaintMapper;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springblade.ms.reportcomplaint.service.IReportComplaintService;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springblade.ms.reportcomplaint.wrapper.ReportComplaintWrapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 工单信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
@AllArgsConstructor
public class ReportComplaintServiceImpl extends BaseServiceImpl<ReportComplaintMapper, ReportComplaintEntity> implements IReportComplaintService {

	private final IReportIllegalLabelService reportIllegalLabelService;

	@Override
	public IPage<ReportComplaintVO> selectReportComplaintPage(IPage<ReportComplaintVO> page, ReportComplaintVO reportComplaint) {
		List<ReportComplaintVO> reportComplaintVOS = selectPage(page, reportComplaint);

		reportComplaintVOS.forEach(reportComplaintVO -> {
			if(ObjectUtil.isNotNull(reportComplaintVO.getUserId())){
				User updateUser = UserCache.getUser(reportComplaintVO.getUserId());
				reportComplaintVO.setUserName(updateUser.getName());
			}
		});
		return page.setRecords(reportComplaintVOS);
	}

	@Override
	public List<ReportComplaintVO> selectPage(IPage<ReportComplaintVO> page, ReportComplaintVO reportComplaint) {
		List<ReportComplaintVO> reportComplaintVOS = baseMapper.selectReportComplaintPage(page, reportComplaint);
		// 获取标签
		for (ReportComplaintVO reportComplaintVO : reportComplaintVOS) {
			List<ReportIllegalLabelVO> reportIllegalLabelVOS = reportIllegalLabelService.selectListByObjId(reportComplaintVO.getId());
			reportComplaintVO.setReportIllegalLabelList(reportIllegalLabelVOS);
		}

		return reportComplaintVOS;
	}

	@Override
	public List<ReportComplaintExcel> exportReportComplaint(Wrapper<ReportComplaintEntity> queryWrapper) {
		List<ReportComplaintExcel> reportComplaintList = baseMapper.exportReportComplaint(queryWrapper);
		return reportComplaintList;
	}

	@Override
	public ReportComplaintEntity getReportComplaintByRollNumber(String rollNumber) {
		if (StrUtil.isBlank(rollNumber)) {
			return null;
		}

		return baseMapper.getReportComplaintByRollNumber(rollNumber);
	}

}
