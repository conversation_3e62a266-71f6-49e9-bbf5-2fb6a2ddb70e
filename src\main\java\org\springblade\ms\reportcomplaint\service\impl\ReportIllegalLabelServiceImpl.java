package org.springblade.ms.reportcomplaint.service.impl;

import cn.hutool.core.collection.IterUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.ms.basic.service.IProductInfoService;
import org.springblade.ms.basic.service.IYhytLicenseService;
import org.springblade.ms.reportcomplaint.mapper.ReportIllegalLabelMapper;
import org.springblade.ms.reportcomplaint.pojo.entity.ReportIllegalLabelEntity;
import org.springblade.ms.reportcomplaint.pojo.vo.ReportIllegalLabelVO;
import org.springblade.ms.reportcomplaint.service.IReportIllegalLabelService;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-16 23:41
 */
@Service
@AllArgsConstructor
public class ReportIllegalLabelServiceImpl extends BaseServiceImpl<ReportIllegalLabelMapper, ReportIllegalLabelEntity> implements IReportIllegalLabelService {

    private final IYhytLicenseService yhytLicenseService;
    private final IProductInfoService productInfoService;
    private final CacheManager cacheManager;


    @Override
    @Cacheable(cacheNames = "ms:reportIllegalLabel", key = "'selectListByObjId:' + #objId")
    public List<ReportIllegalLabelVO> selectListByObjId(Long objId) {
        if (objId == null) {
            return null;
        }

        ReportIllegalLabelEntity paramObj = new ReportIllegalLabelEntity();
        paramObj.setObjId(objId);

        List<ReportIllegalLabelVO> dbList = baseMapper.selectList(paramObj);
        if (IterUtil.isEmpty(dbList)) {
            return null;
        }

        for (ReportIllegalLabelVO reportIllegalLabelVO : dbList) {
            // 填充标签名称
            switch (reportIllegalLabelVO.getLabelType()) {
                case "品规":
                    reportIllegalLabelVO.setProductInfo(productInfoService.getById(reportIllegalLabelVO.getLabelId()));
                    break;
                case "零售户":
                    reportIllegalLabelVO.setYhytLicense(yhytLicenseService.getById(reportIllegalLabelVO.getLabelId()));
                    break;
            }
        }

        return dbList;
    }

    @Override
    public Boolean saveOrUpdate(Long objId, List<ReportIllegalLabelVO> reportIllegalLabelList) {
        if (objId == null) {
            return null;
        }


        // 获取自身的代理对象，触发缓存
        ReportIllegalLabelEntity paramObj = new ReportIllegalLabelEntity();
        paramObj.setObjId(objId);
        List<ReportIllegalLabelVO> dbList = baseMapper.selectList(paramObj);

        // 对传进来的集合和数据库的集合进行差集，得出需要删除、需要新增、需要更新的集合
        // 使用HashSet来存储对象，便于快速查找
        Set<ReportIllegalLabelVO> reportSet = new HashSet<>(reportIllegalLabelList);
        Set<ReportIllegalLabelVO> dbSet = new HashSet<>(dbList);

        // 找出需要新增的集合
        List<ReportIllegalLabelVO> toAdd = new ArrayList<>(reportSet);
        toAdd.removeAll(dbSet);

        // 找出需要删除的集合
        List<ReportIllegalLabelVO> toDelete = new ArrayList<>(dbSet);
        toDelete.removeAll(reportSet);

        // 将toAddOrUpdate的VO集合转换为Entity集合
        List<ReportIllegalLabelEntity> toAddEntities = new ArrayList<>();
        for (ReportIllegalLabelVO vo : toAdd) {
            ReportIllegalLabelEntity entity = new ReportIllegalLabelEntity();
            entity.setId(vo.getId());
            entity.setObjId(vo.getObjId());
            entity.setObjType(vo.getObjType());
            entity.setLabelType(vo.getLabelType());
            entity.setLabelId(vo.getLabelId());
            // 其他字段的转换
            toAddEntities.add(entity);
        }


        // 处理新增和更新逻辑
        if (!toAddEntities.isEmpty()) {
            this.saveOrUpdateBatch(toAddEntities);
        }

        // 处理删除逻辑
        if (!toDelete.isEmpty()) {
            List<Long> deleteIds = toDelete.stream().map(ReportIllegalLabelVO::getId).collect(Collectors.toList());
            this.deleteLogic(deleteIds);
        }

        Cache cache = cacheManager.getCache("ms:reportIllegalLabel");
        if (cache != null) {
            cache.evict("selectListByObjId:" + objId);
        }


        return true;
    }

    @Override
    public Long getIllegalRecordsLastYearCount(Long yhytId){
        return baseMapper.getIllegalRecordsLastYearCount(yhytId);
    }

    @Override
    public List<ReportIllegalLabelVO> listByLabelId(Long labelId) {
        return baseMapper.listByLabelId(labelId);
    }
}
