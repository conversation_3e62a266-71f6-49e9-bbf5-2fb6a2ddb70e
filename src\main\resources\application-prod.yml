#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      ##将docker脚本部署的redis服务映射为宿主机ip
      ##生产环境推荐使用阿里云高可用redis服务并设置密码
      host: **********
      port: 6379
      password: ystech@2025!
      database: 0
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    url: *****************************************
    username: root
    password: Ystech@2025

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://**********:6379
    password:
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html


#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://**********:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://10-78-81-8-aawey4n5ob9ct3.ztna-dingtalk.com
  #访问key
  access-key: OkL85reOLjtr30xaPjtV
  #密钥key
  secret-key: OR7oala7v39rHB4CC7r1zR9479FDZVSXmYlD4ftU
  #存储桶
  bucket-name: images

# 共享中心接口配置
share:
  api:
    url: https://************:8443/GatewayMsg/http/api/proxy/invoke
    paasid: ap25022LRCZH
    token: 2718549fe9f94bb8b4d62444f18c891c

ding-app:
  agent-id: **********
  mini-app-id:
  app-key: dingcplrqegxs3yno3u0
  app-secret: 5jKs2u1VhjvsPSBUjeG3mMlTiuD-dNXvE7D3dlgwRmI22YexNA1ExGG5-Ix6L7VN
  corp-id: dingc76a2a37b24a860a4ac5d6980864d335
  api-token:
  #品规识别地址
  detect-url: http://localhost:8088/detect

# 12345平台配置
platform:
  12345:
    api-root: http://************:8010
    account: zjycjjk
    password: Zjycjjk!@0528
    appid: 58a2754ea2df400c9fa35b409ca56780